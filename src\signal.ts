type SignalEffect<T> = (value: T) => void | Promise<void>;
type Signal<T> = (() => T) &
    ((newValue: T) => void) &
    ((effect: SignalEffect<T>) => void);

function signal<T = number | string | boolean>(initValue: T): Signal<T> {
    const effects = new Set<() => void>();
    let value = initValue;

    return (newValue: T | undefined | ((value: T) => void)) => {
        if (arguments.length === 0) return value;

        if (typeof newValue === "function") {
            effects.add(newValue);
            return;
        }

        if (typeof newValue === "undefined") {
            return value;
        }

        queueMicrotask(() =>
            effects.forEach((f: SignalEffect<T>) => f(newValue)),
        );
        value = newValue;
    };
}

const size = signal(0);

size();

size(10);

size(() => {
    console.log("size changed", size());
});
